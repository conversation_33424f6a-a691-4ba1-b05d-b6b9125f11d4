llm:
- api_key: <API-KEY> # LLM API key
  base_url: <BASE-URL> # LLM base URL, used for VLLM
  model: <YOUR-MODEL> # LLM model
  provider: <PROVIDER> # LLM provider
  semaphore: 200 # Semaphore for LLM requests, control the max number of concurrent requests
env:
  avro:
    enabled: false # Whether to enable Avro
  mlflow:
    enabled: false # Whether to enable MLflow
    mlflow_uri: http://localhost:59000 # MLflow server URI``
    username: <CHANGE_ME> # MLflow server username
    password: <CHANGE_ME> # MLflow server password
  pgsql:
    enabled: true # Whether to enable PostgreSQL
    dsn: postgresql://postgres:CHANGE_ME@localhost:5432/postgres # PostgreSQL connection string
map:
  file_path: <MAP-FILE-PATH> # Path to the map file
  cache_path: <CACHE-FILE-PATH> # Cache path for accelerating map file loading
agents:
  citizens:
  - agent_class: citizen # The class of the agent
    number: 100 # The number of the agents
exp:
  name: test # Experiment name
  environment:
    start_tick: 28800 # Start time in seconds
  workflow:
  - day: 1 # The day of the workflow step
    type: run # The type of the workflow step
