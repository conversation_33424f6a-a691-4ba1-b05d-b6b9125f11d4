[build-system]
requires = ["setuptools>=61.0", "requests>=2", "cibuildwheel"]
build-backend = "setuptools.build_meta"

[project]
name = "agentsociety"
version = "1.5.0a2" # change it for each release
description = "LLM-based city environment agent building library"
authors = [
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON> Yan", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>" },
]
license = { file = "LICENSE" }
readme = "README.md"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: POSIX :: Linux",
    "Operating System :: MacOS :: MacOS X",
]
requires-python = ">=3.11"
dependencies = [
    "aiodocker>=0.24.0",
    "asyncpg>=0.30.0",
    "boto3>=1.37",
    "click>=8.0.0",
    "cramjam>=2.9.0",
    "faiss-cpu>=1.9.0.post1",
    "fastapi>=0.103.1",
    "fastavro>=1.10.0",
    "geojson>=3.1.0",
    "grpcio>=1.71.0,<2.0.0",
    "json-with-comments>=1.2.10",
    "langchain-community>=0.3.13",
    "langchain-core>=0.3.28",
    "mlflow>=2.19.0",
    "networkx>=3.2.1",
    "numpy>=1.20.0,<2.0.0",
    "openai>=1.58.1",
    "protobuf<=4.24.0,<5.0.0",
    "psutil>=5.9.0",
    "psycopg[binary]>=3.2.3",
    "pycityproto>=2.2.7",
    "pydantic>=2.10.4",
    "pyproj>=3.6.0",
    "python-dotenv>=1.0.0",
    "python-multipart>=0.0.20",
    "PyYAML>=6.0.2",
    "ray[default]>=2.40.0",
    "requests>=2.32.3",
    "Shapely>=2.0.6",
    "sqlalchemy[asyncio]>=2.0.20",
    "torch>=2.5.1",
    "transformers>=4.47.1",
    "uvicorn>=0.23.2"
]

[project.urls]
Homepage = "https://github.com/tsinghua-fib-lab/agentsociety"
Repository = "https://github.com/tsinghua-fib-lab/agentsociety.git"
Issues = "https://github.com/tsinghua-fib-lab/agentsociety/issues"

[project.scripts]
agentsociety = "agentsociety.cli.cli:cli"
agentsociety-sim = "agentsociety.cli.sim:agentsociety_sim"

[tool.setuptools.packages.find]
where = ["."]
include = ["agentsociety*"]

[tool.setuptools.package-data]
"agentsociety._dist" = ["*", "**/*"]

[tool.cibuildwheel]
build = ["cp3*manylinux_x86_64", "cp3*macosx_arm64"]
skip = ["cp36-*", "cp37-*", "cp38-*", "cp39-*", "cp310-*"]

[tool.cibuildwheel.linux]
repair-wheel-command = "auditwheel repair -w {dest_dir} {wheel}"

[tool.cibuildwheel.macos]
repair-wheel-command = "delocate-wheel -w {dest_dir} {wheel}"
