services:
  mlflow-svc:
    # because Docker Desktop cannot attach folders in WSL2, we need to build the image locally
    build:
      context: ./mlflow
      dockerfile: Dockerfile
      args:
        BASE_IMAGE: swr.cn-north-4.myhuaweicloud.com/tsinghua-fib-lab/mlflow:2.19.0
    container_name: mlflow
    restart: always
    ports:
      - 59000:59000
    volumes:
      - mlflow:/app
    environment:
      MLFLOW_AUTH_CONFIG_PATH: /mlflow/basic_auth.ini
      PG_DSN: ***************************************************/postgres
    depends_on:
      - postgresql-svc

  postgresql-svc:
    image: swr.cn-north-4.myhuaweicloud.com/tsinghua-fib-lab/postgresql:17
    container_name: postgresql
    restart: unless-stopped
    ports:
      - "5432:5432"
    volumes:
      - postgresql:/bitnami/postgresql
    environment:
      POSTGRESQL_PASSWORD: CHANGE_ME

volumes:
  mlflow:
  postgresql:
