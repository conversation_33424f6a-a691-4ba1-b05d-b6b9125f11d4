import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

i18n
    // detect user language
    // learn more: https://github.com/i18next/i18next-browser-languageDetector
    .use(LanguageDetector)
    // pass the i18n instance to react-i18next.
    .use(initReactI18next)
    // init i18next
    // for all options read: https://www.i18next.com/overview/configuration-options
    .init({
        debug: true,
        fallbackLng: 'zh',
        interpolation: {
            escapeValue: false, // not needed for react as it escapes by default
        },
        resources: {
            en: {
                translation: {
                    menu: {
                        experiments: 'Experiments',
                        survey: 'Survey',
                        documentation: 'Documentation',
                        github: 'GitHub',
                        mlflow: 'MLFlow',
                        llmConfigs: 'LLM',
                        maps: 'Maps',
                        agents: 'Agents',
                        agentConfigs: 'Agent Configs',
                        agentTemplates: 'Agent Templates',
                        workflows: 'Workflows',
                        create: 'Create',
                        login: 'Login',
                        logout: 'Logout',
                        account: 'Account',
                        demo: 'Demo',
                        demoUser: 'Demo User',
                        profiles: 'Profiles',
                    },
                    home: {
                        whatsNew: "What's New",
                        releaseNotes: "Release v1.3. Click here to view the release notes.",
                        getStarted: "Get Started",
                        stars: "Stars",
                        mainDescription: "Create your society with <strong><em>Large Model-driven Social Human Agent</em></strong> and <strong><em>Realistic Urban Social Environment</em></strong>"
                    },
                    survey: {
                        createSurvey: "Create Survey",
                        editSurvey: "Edit Survey",
                        surveyName: "Survey Name",
                        surveyJsonData: "Survey JSON Data",
                        onlineVisualEditor: "Online Visual Editor",
                        submit: "Submit",
                        delete: "Delete",
                        deleteConfirm: "Are you sure to delete this survey?",
                        createSuccess: "Create success!",
                        updateSuccess: "Update success!",
                        deleteSuccess: "Delete success!",
                        createFailed: "Create failed:",
                        updateFailed: "Update failed:",
                        deleteFailed: "Delete failed:",
                        fetchFailed: "Fetch surveys failed:",
                        invalidJson: "Invalid JSON format",
                        pleaseInputName: "Please input name",
                        pleaseInputData: "Please input data JSON",
                        table: {
                            id: "ID",
                            name: "Name",
                            data: "Data",
                            createdAt: "Created At",
                            updatedAt: "Updated At",
                            action: "Action",
                            edit: "Edit",
                            delete: "Delete"
                        },
                    },
                    console: {
                        table: {
                            id: "ID",
                            name: "Name",
                            numDay: "Num Day",
                            status: "Status",
                            currentDay: "Current Day",
                            currentTime: "Current Time",
                            config: "Config",
                            error: "Error",
                            inputTokens: "Input Tokens",
                            outputTokens: "Output Tokens",
                            createdAt: "Created At",
                            updatedAt: "Updated At",
                            action: "Action"
                        },
                        statusEnum: {
                            "0": "Not Started",
                            "1": "Running",
                            "2": "Completed",
                            "3": "Error Interrupted",
                            "4": "Stopped"
                        },
                        buttons: {
                            goto: "Goto",
                            stop: "Stop",
                            detail: "Detail",
                            viewLog: "View Log",
                            export: "Export All",
                            exportArtifacts: "Export Artifacts",
                            delete: "Delete",
                            createExperiment: "Create Experiment"
                        },
                        modals: {
                            experimentDetail: "Experiment Detail",
                            experimentLog: "Experiment Log",
                            refresh: "Refresh",
                            manualRefresh: "Manual refresh",
                            refreshing: "Refreshing...",
                            refreshIntervals: {
                                oneSecond: "Every 1 second",
                                fiveSeconds: "Every 5 seconds",
                                tenSeconds: "Every 10 seconds",
                                thirtySeconds: "Every 30 seconds"
                            }
                        },
                        confirmations: {
                            stopExperiment: "Are you sure to stop this experiment?",
                            deleteExperiment: "Are you sure to delete this experiment?"
                        },
                        messages: {
                            stopSuccess: "Stop experiment successfully",
                            stopFailed: "Failed to stop experiment:",
                            deleteSuccess: "Delete experiment successfully",
                            deleteFailed: "Failed to delete experiment:",
                            noToken: "No token found, please login"
                        }
                    },
                    replay: {
                        day: "Day {{day}}",
                        chatbox: {
                            tabs: {
                                reflection: "Reflection",
                                agent: "Agent",
                                user: "User",
                                survey: "Survey",
                                metrics: "Metrics"
                            },
                            survey: {
                                preview: "Preview",
                                surveyName: "Survey Name",
                                surveySent: "Survey sent, you should wait for the agent to save the survey into database and respond",
                                messageSent: "Message sent, you should wait for the agent to save the message into database and respond",
                                sendFailed: "Failed to send:"
                            },
                            dialog: {
                                sendSuccess: "Message sent, you should wait for the agent to save the message into database and respond"
                            },
                            metrics: {
                                noMetrics: "No metrics data available",
                                step: "Step",
                                value: "Value"
                            }
                        },
                        infoPanel: {
                            title: "Agent Information",
                            chooseAgent: "Please choose an agent in map",
                            unknown: "[Unknown]",
                            currentStatus: "Current Status",
                            statusHistory: "Status History",
                            name: "name",
                            id: "ID",
                            showAsHeatmap: "Click to show as heatmap"
                        },
                        timelinePlayer: {
                            replay: "Replay",
                            live: "Live",
                            stepSpeed: {
                                "10s": "10s/step",
                                "5s": "5s/step",
                                "2s": "2s/step",
                                "1s": "1s/step",
                                "0.5s": "0.5s/step",
                                "0.25s": "0.25s/step",
                                "0.1s": "0.1s/step"
                            }
                        }
                    },
                    form: {
                        experiment: {
                            createTitle: "Create Experiment",
                            startButton: "Start Experiment",
                            nameLabel: "Experiment Name",
                            namePlaceholder: "Enter experiment name",
                            nameRequired: "Please enter experiment name",
                            componentsTitle: "Experiment Components",
                            llmTitle: "LLM Configuration",
                            mapTitle: "Map Configuration",
                            agentTitle: "Agent Configuration",
                            workflowTitle: "Workflow Configuration",
                            createNew: "Create",
                            selectLLM: "Select LLM Configuration",
                            selectLLMPlaceholder: "Select LLM configuration",
                            llmRequired: "Please select LLM configuration",
                            selectMap: "Select Map",
                            selectMapPlaceholder: "Select map",
                            mapRequired: "Please select map",
                            selectAgent: "Select Agent",
                            selectAgentPlaceholder: "Select agent",
                            agentRequired: "Please select agent",
                            selectWorkflow: "Select Workflow",
                            selectWorkflowPlaceholder: "Select workflow",
                            workflowRequired: "Please select workflow",
                            statusLabel: "Status",
                            statusStarting: "Starting",
                            statusFailed: "Failed to Start",
                            statusError: "Error Occurred",
                            statusPreparing: "Preparing",
                            statusStartingDesc: "Experiment is starting, please wait...",
                            statusInitializingDesc: "Experiment is initializing, please wait...",
                            statusFailedDesc: "Failed to start experiment, please check configuration and try again",
                            statusPreparingDesc: "Experiment is preparing, please wait...",
                            messages: {
                                startFailed: "Failed to start experiment: {{error}}",
                                startSuccess: "Experiment started successfully",
                                initSuccess: "Experiment initialized successfully",
                                statusCheckFailed: "Failed to check experiment status",
                                loadFailed: "Failed to load experiment"
                            }
                        },
                        agent: {
                            title: "Agent Configuration",
                            createNew: "Create",
                            searchPlaceholder: "Search agents",
                            editTitle: "Edit Agent",
                            createTitle: "Create Agent",
                            metadataTitle: "Configuration Metadata",
                            settingsTitle: "Agent Settings",
                            templates: "Agent Templates",
                            profiles: "Profiles",
                            citizenGroup: "Citizen Group",
                            citizenGroupPlaceholder: "Select citizen group",
                            pleaseSelectCitizenGroup: "Please select citizen group",
                            institutions: "Institutions",
                            firmGroup: "Firm",
                            governmentGroup: "Government",
                            bankGroup: "Bank",
                            nbsGroup: "NBS",
                            addGroup: "Add Group",
                            selectTemplate: "Select Template",
                            selectTemplatePlaceholder: "Select agent template",
                            numberLabel: "Number",
                            numberPlaceholder: "Enter number of agents",
                            selectProfile: "Select Profile",
                            selectProfilePlaceholder: "Select agent profile",
                            pleaseInputFirmNumber: "Please input firm number",
                            pleaseInputGovernmentNumber: "Please input government number",
                            pleaseInputBankNumber: "Please input bank number",
                            pleaseInputNbsNumber: "Please input NBS number",
                            messages: {
                                loadFailed: "Failed to load agents",
                                deleteSuccess: "Agent deleted successfully",
                                deleteFailed: "Failed to delete agent",
                                updateSuccess: "Agent updated successfully",
                                updateFailed: "Failed to update agent",
                                createSuccess: "Agent created successfully",
                                createFailed: "Failed to create agent"
                            }
                        },
                        llm: {
                            title: "LLM Configurations",
                            createNew: "Create",
                            searchPlaceholder: "Search LLM configs",
                            editTitle: "Edit LLM Config",
                            createTitle: "Create LLM Config",
                            metadataTitle: "Configuration Metadata",
                            settingsTitle: "LLM Config Settings",
                            providerTitle: "LLM Provider",
                            providerLabel: "Provider",
                            providerPlaceholder: "Select LLM provider",
                            baseUrl: "Base URL",
                            baseUrlPlaceholder: "Enter base URL if using a custom endpoint",
                            apiKey: "API Key",
                            apiKeyPlaceholder: "Enter API key",
                            model: "Model",
                            modelPlaceholder: "Select model",
                            vllmModelPlaceholder: "Enter vLLM model name",
                            addProvider: "Add LLM Provider"
                        },
                        map: {
                            title: "Map Configurations",
                            createNew: "Create",
                            searchPlaceholder: "Search maps",
                            editTitle: "Edit Map",
                            createTitle: "Create Map",
                            metadataTitle: "Configuration Metadata",
                            settingsTitle: "Map Settings",
                            uploadTitle: "New Map File",
                            uploadHint: "Click or drag map file to this area to upload",
                            uploadDescription: "Please upload a single .pb file."
                        },
                        common: {
                            name: "Name",
                            namePlaceholder: "Enter configuration name",
                            nameRequired: "Please enter a name for this configuration",
                            description: "Description",
                            descriptionPlaceholder: "Enter a description for this configuration",
                            edit: "Edit",
                            delete: "Delete",
                            deleteConfirm: "Are you sure you want to delete this configuration?",
                            duplicate: "Duplicate",
                            export: "Export",
                            lastUpdated: "Last Updated",
                            actions: "Actions",
                            submit: "Submit",
                            cancel: "Cancel",
                            metadataTitle: "Basic Information",
                            view: "View"
                        },
                        template: {
                            title: "Agent Templates",
                            createNew: "Create",
                            searchPlaceholder: "Search templates",
                            editTitle: "Edit Template",
                            createTitle: "Create Template",
                            basicInfo: "Basic Information",
                            namePlaceholder: "General Social Agent",
                            descriptionPlaceholder: "Enter template description",
                            agentType: "Agent Type",
                            agentClass: "Agent Class",
                            selectAgentType: "Select Agent Type",
                            selectAgentClass: "Select Agent Class",
                            selectAgentTypeFirst: "Please select Agent Type first",
                            pleaseSelectAgentType: "Please select Agent Type",
                            pleaseSelectAgentClass: "Please select Agent Class",
                            selectAgentTypeAndClass: "Please select Agent Type and Agent Class first",
                            agentTypes: {
                                citizen: "Citizen",
                                supervisor: "Supervisor"
                            },
                            profileSection: "Profile Configuration",
                            baseSection: "Base Configuration",
                            agentConfig: "Agent Configuration",
                            blockConfig: "Block Configuration",
                            selectBlocks: "Select Blocks",
                            selectBlocksPlaceholder: "Select blocks to configure",
                            choiceWeights: "Choice Weights",
                            choiceWeightsTooltip: "Sum of weights should be 1",
                            required: "Required",
                            option: "Option",
                            weight: "Weight",
                            distributionType: "Distribution Type",
                            uniformDistribution: "Uniform Distribution",
                            normalDistribution: "Normal Distribution",
                            minValue: "Minimum Value",
                            maxValue: "Maximum Value",
                            mean: "Mean",
                            standardDeviation: "Standard Deviation",
                            discreteChoice: "Discrete Choice",
                            baseLocation: "Base Location",
                            homeAreaId: "Home Area ID",
                            workAreaId: "Work Area ID",
                            messages: {
                                createSuccess: "Template created successfully",
                                createFailed: "Failed to create template",
                                updateSuccess: "Template updated successfully",
                                updateFailed: "Failed to update template",
                                fetchFailed: "Failed to fetch template"
                            }
                        },
                        workflow: {
                            title: "Workflow Configurations",
                            createNew: "Create",
                            searchPlaceholder: "Search workflows",
                            editTitle: "Edit Workflow",
                            createTitle: "Create Workflow",
                            settingsTitle: "Workflow Settings",
                            deleteConfirm: "Are you sure you want to delete this workflow?",
                            workflowSteps: "Workflow Steps",
                            defaultRunDescription: "Run a 1-day simulation",
                            step: "Step",
                            stepTooltip: "Run a single step simulation",
                            stepType: "Step Type",
                            pleaseSelectStepType: "Please select step type",
                            selectStepType: "Select Step Type",
                            run: "Run",
                            runTooltip: "Run a typical day simulation",
                            environmentIntervene: "Environment Intervene",
                            environmentInterveneTooltip: "Update environment information, such as weather information",
                            nextRound: "Next Round",
                            nextRoundTooltip: "Reset agent and prepare to start next round simulation",
                            function: "Function",
                            functionTooltip: "Run a specific function",
                            functionName: "Function Name",
                            functionNameTooltip: "Select function to run",
                            pleaseSelectFunction: "Please select function",
                            selectFunction: "Select Function",
                            days: "Days",
                            pleaseEnterDays: "Please enter days",
                            daysTooltip: "Duration of this step in days",
                            ticksPerStep: "Ticks per Step",
                            ticksPerStepTooltip: "Ticks per step in environment",
                            steps: "Steps",
                            pleaseEnterSteps: "Please enter steps",
                            stepsTooltip: "Duration of this step in steps",
                            environmentKey: "Environment Key",
                            pleaseEnterEnvironmentKey: "Please enter environment key",
                            environmentKeyTooltip: "Environment intervene key identifier",
                            enterEnvironmentKey: "Enter environment key",
                            environmentValue: "Environment Value",
                            pleaseEnterEnvironmentValue: "Please enter environment value",
                            environmentValueTooltip: "Environment key value to set",
                            enterEnvironmentValue: "Enter environment value",
                            description: "Description",
                            descriptionTooltip: "Description of this workflow step",
                            enterStepDescription: "Enter step description",
                            addWorkflowStep: "Add Workflow Step",
                            messages: {
                                loadFailed: "Failed to load workflows",
                                deleteSuccess: "Workflow deleted successfully",
                                deleteFailed: "Failed to delete workflow",
                                updateSuccess: "Workflow updated successfully",
                                updateFailed: "Failed to update workflow",
                                createSuccess: "Workflow created successfully",
                                createFailed: "Failed to create workflow"
                            }
                        },
                        profile: {
                            title: "Agent Profiles",
                            createNew: "Create",
                            searchPlaceholder: "Search profiles",
                            editTitle: "Edit Profile",
                            createTitle: "Create Profile",
                            metadataTitle: "Profile Metadata",
                            settingsTitle: "Profile Settings",
                            basicInfo: "Basic Information",
                            namePlaceholder: "Enter profile name",
                            descriptionPlaceholder: "Enter profile description",
                            personality: "Personality Traits",
                            personalityPlaceholder: "Enter personality traits",
                            background: "Background Story",
                            backgroundPlaceholder: "Enter background story",
                            preferences: "Preferences",
                            preferencesPlaceholder: "Enter preferences",
                            uploadProfile: "Upload Profile",
                            uploadTitle: "Upload Profile",
                            uploadHint: "Click or drag file to this area to upload",
                            uploadDescription: "Support for JSON files. The file should contain agent profile data.",
                            enterDescription: "Enter description for this profile",
                            cancel: "Cancel",
                            upload: "Upload",
                            pleaseSelectFile: "Please select a file to upload",
                            messages: {
                                loadFailed: "Failed to load profiles",
                                deleteSuccess: "Profile deleted successfully",
                                deleteFailed: "Failed to delete profile",
                                updateSuccess: "Profile updated successfully",
                                updateFailed: "Failed to update profile",
                                createSuccess: "Profile created successfully",
                                createFailed: "Failed to create profile",
                                uploadSuccess: "Profile uploaded successfully",
                                uploadFailed: "Failed to upload profile",
                                noData: "No data available for download",
                                deleteConfirm: "Are you sure you want to delete this profile?"
                            },
                            table: {
                                name: "Name",
                                description: "Description",
                                count: "Count",
                                createdAt: "Created At",
                                actions: "Actions",
                                download: "Download",
                                delete: "Delete"
                            }
                        }
                    }
                }
            },
            zh: {
                translation: {
                    menu: {
                        experiments: '实验',
                        survey: '问卷',
                        documentation: '文档',
                        github: 'GitHub',
                        mlflow: 'MLFlow',
                        llmConfigs: 'LLM',
                        maps: '地图',
                        agents: '智能体',
                        agentConfigs: '智能体配置',
                        agentTemplates: '智能体模板',
                        workflows: '工作流',
                        create: '创建',
                        login: '登录',
                        logout: '退出登录',
                        account: '账户',
                        demo: '示例',
                        demoUser: '示例用户',
                        profiles: '智能体画像',
                    },
                    home: {
                        whatsNew: "最新动态",
                        releaseNotes: "V1.3版本发布。点击此处查看发布说明。",
                        getStarted: "开始使用",
                        stars: "星标",
                        mainDescription: "使用<strong><em>大模型驱动的社会人智能体</em></strong>和<strong><em>真实城市社会环境</em></strong>构建虚拟社会"
                    },
                    survey: {
                        createSurvey: "创建问卷",
                        editSurvey: "编辑问卷",
                        surveyName: "问卷名称",
                        surveyJsonData: "问卷JSON数据",
                        onlineVisualEditor: "在线可视化编辑器",
                        submit: "提交",
                        delete: "删除",
                        deleteConfirm: "确定要删除这个问卷吗？",
                        createSuccess: "创建成功！",
                        updateSuccess: "更新成功！",
                        deleteSuccess: "删除成功！",
                        createFailed: "创建失败：",
                        updateFailed: "更新失败：",
                        deleteFailed: "删除失败：",
                        fetchFailed: "获取问卷失败：",
                        invalidJson: "JSON格式无效",
                        pleaseInputName: "请输入名称",
                        pleaseInputData: "请输入JSON数据",
                        table: {
                            id: "ID",
                            name: "名称",
                            data: "数据",
                            createdAt: "创建时间",
                            updatedAt: "更新时间",
                            action: "操作",
                            edit: "编辑",
                            delete: "删除"
                        },
                    },
                    console: {
                        table: {
                            id: "ID",
                            name: "名称",
                            numDay: "天数",
                            status: "状态",
                            currentDay: "当前天数",
                            currentTime: "当前时间",
                            config: "配置",
                            error: "报错",
                            inputTokens: "输入Token数",
                            outputTokens: "输出Token数",
                            createdAt: "创建时间",
                            updatedAt: "更新时间",
                            action: "操作"
                        },
                        statusEnum: {
                            "0": "未开始",
                            "1": "运行中",
                            "2": "已完成",
                            "3": "错误中断",
                            "4": "已停止"
                        },
                        buttons: {
                            goto: "查看",
                            stop: "停止",
                            detail: "详情",
                            viewLog: "查看日志",
                            export: "全部导出",
                            exportArtifacts: "导出产物",
                            delete: "删除",
                            createExperiment: "创建实验"
                        },
                        modals: {
                            experimentDetail: "实验详情",
                            experimentLog: "实验日志",
                            refresh: "刷新",
                            manualRefresh: "手动刷新",
                            refreshing: "正在刷新...",
                            refreshIntervals: {
                                oneSecond: "每秒刷新",
                                fiveSeconds: "每5秒刷新",
                                tenSeconds: "每10秒刷新",
                                thirtySeconds: "每30秒刷新"
                            }
                        },
                        confirmations: {
                            stopExperiment: "确定要停止这个实验吗？",
                            deleteExperiment: "确定要删除这个实验吗？"
                        },
                        messages: {
                            stopSuccess: "停止实验成功",
                            stopFailed: "停止实验失败：",
                            deleteSuccess: "删除实验成功",
                            deleteFailed: "删除实验失败：",
                            noToken: "未找到token，请登录"
                        }
                    },
                    replay: {
                        day: "第{{day}}天",
                        chatbox: {
                            tabs: {
                                reflection: "反思",
                                agent: "智能体",
                                user: "用户",
                                survey: "问卷",
                                metrics: "指标"
                            },
                            dialog: {
                                sendSuccess: "消息已发送，请等待智能体将消息保存到数据库并响应"
                            },
                            survey: {
                                preview: "预览",
                                surveyName: "问卷名称",
                                surveySent: "问卷已发送，请等待智能体将问卷保存到数据库并响应",
                                messageSent: "消息已发送，请等待智能体将消息保存到数据库并响应",
                                sendFailed: "发送失败："
                            },
                            metrics: {
                                noMetrics: "没有可用的指标数据",
                                step: "步数",
                                value: "值"
                            }
                        },
                        infoPanel: {
                            title: "智能体信息",
                            chooseAgent: "请在地图中选择一个智能体",
                            unknown: "[未知]",
                            currentStatus: "当前状态",
                            statusHistory: "状态历史",
                            name: "名称",
                            id: "ID",
                            showAsHeatmap: "点击显示为热力图"
                        },
                        timelinePlayer: {
                            replay: "回放",
                            live: "直播",
                            stepSpeed: {
                                "10s": "10秒/步",
                                "5s": "5秒/步",
                                "2s": "2秒/步",
                                "1s": "1秒/步",
                                "0.5s": "0.5秒/步",
                                "0.25s": "0.25秒/步",
                                "0.1s": "0.1秒/步"
                            }
                        }
                    },
                    form: {
                        experiment: {
                            createTitle: "创建实验",
                            startButton: "开始实验",
                            nameLabel: "实验名称",
                            namePlaceholder: "请输入实验名称",
                            nameRequired: "请输入实验名称",
                            componentsTitle: "实验组件",
                            llmTitle: "LLM配置",
                            mapTitle: "地图配置",
                            agentTitle: "智能体配置",
                            workflowTitle: "工作流配置",
                            createNew: "新建",
                            selectLLM: "选择LLM配置",
                            selectLLMPlaceholder: "请选择LLM配置",
                            llmRequired: "请选择LLM配置",
                            selectMap: "选择地图",
                            selectMapPlaceholder: "请选择地图",
                            mapRequired: "请选择地图",
                            selectAgent: "选择智能体",
                            selectAgentPlaceholder: "请选择智能体",
                            agentRequired: "请选择智能体",
                            selectWorkflow: "选择工作流",
                            selectWorkflowPlaceholder: "请选择工作流",
                            workflowRequired: "请选择工作流",
                            statusLabel: "状态",
                            statusStarting: "正在启动",
                            statusFailed: "启动失败",
                            statusError: "发生错误",
                            statusPreparing: "正在准备",
                            statusStartingDesc: "实验正在启动中，请稍候...",
                            statusInitializingDesc: "实验正在初始化中，请稍候...",
                            statusFailedDesc: "实验启动失败，请检查配置后重试",
                            statusPreparingDesc: "实验正在准备中，请稍候...",
                            messages: {
                                startFailed: "启动实验失败",
                                startSuccess: "实验启动成功",
                                initSuccess: "实验初始化成功",
                                statusCheckFailed: "检查实验状态失败",
                                loadFailed: "加载实验失败"
                            }
                        },
                        agent: {
                            title: "智能体配置",
                            createNew: "新建",
                            searchPlaceholder: "搜索智能体",
                            editTitle: "编辑智能体",
                            createTitle: "创建智能体",
                            metadataTitle: "配置元数据",
                            settingsTitle: "智能体设置",
                            templates: "智能体模板",
                            profiles: "智能体画像",
                            citizenGroup: "市民群体",
                            citizenGroupPlaceholder: "请选择市民群体",
                            pleaseSelectCitizenGroup: "请选择市民群体",
                            institutions: "机构",
                            firmGroup: "企业",
                            governmentGroup: "政府",
                            bankGroup: "银行",
                            nbsGroup: "统计局",
                            addGroup: "添加群体",
                            selectTemplate: "选择模板",
                            selectTemplatePlaceholder: "请选择智能体模板",
                            numberLabel: "数量",
                            numberPlaceholder: "请输入智能体数量",
                            selectProfile: "选择画像",
                            selectProfilePlaceholder: "请选择智能体画像",
                            pleaseInputFirmNumber: "请输入企业数量",
                            pleaseInputGovernmentNumber: "请输入政府数量",
                            pleaseInputBankNumber: "请输入银行数量",
                            pleaseInputNbsNumber: "请输入统计局数量",
                            messages: {
                                loadFailed: "加载智能体失败",
                                deleteSuccess: "智能体删除成功",
                                deleteFailed: "删除智能体失败",
                                updateSuccess: "智能体更新成功",
                                updateFailed: "更新智能体失败",
                                createSuccess: "智能体创建成功",
                                createFailed: "创建智能体失败"
                            }
                        },
                        llm: {
                            title: "LLM配置",
                            createNew: "新建",
                            searchPlaceholder: "搜索LLM配置",
                            editTitle: "编辑LLM配置",
                            createTitle: "创建LLM配置",
                            metadataTitle: "配置元数据",
                            settingsTitle: "LLM配置设置",
                            providerTitle: "LLM提供商",
                            providerLabel: "提供商",
                            providerPlaceholder: "选择LLM提供商",
                            baseUrl: "Base URL",
                            baseUrlPlaceholder: "如使用自定义端点请输入Base URL",
                            apiKey: "API密钥",
                            apiKeyPlaceholder: "请输入API密钥",
                            model: "模型",
                            modelPlaceholder: "选择模型",
                            vllmModelPlaceholder: "输入vLLM模型名称",
                            addProvider: "添加LLM提供商"
                        },
                        map: {
                            title: "地图配置",
                            createNew: "新建",
                            searchPlaceholder: "搜索地图",
                            editTitle: "编辑地图",
                            createTitle: "创建地图",
                            metadataTitle: "配置元数据",
                            settingsTitle: "地图设置",
                            uploadTitle: "新地图文件",
                            uploadHint: "点击或拖拽地图文件到此区域上传",
                            uploadDescription: "上传单个.pb文件。"
                        },
                        common: {
                            name: "名称",
                            namePlaceholder: "请输入配置名称",
                            nameRequired: "请输入配置名称",
                            description: "描述",
                            descriptionPlaceholder: "请输入配置描述",
                            edit: "编辑",
                            delete: "删除",
                            deleteConfirm: "确定要删除此配置吗？",
                            duplicate: "复制",
                            export: "导出",
                            lastUpdated: "最后更新时间",
                            actions: "操作",
                            submit: "提交",
                            cancel: "取消",
                            metadataTitle: "基本信息",
                            view: "查看"
                        },
                        template: {
                            title: "智能体模板",
                            createNew: "新建",
                            searchPlaceholder: "搜索模板",
                            editTitle: "编辑模板",
                            createTitle: "创建模板",
                            basicInfo: "基本信息",
                            namePlaceholder: "通用社会智能体",
                            descriptionPlaceholder: "请输入模板描述",
                            agentType: "智能体类型",
                            agentClass: "智能体类",
                            selectAgentType: "请选择智能体类型",
                            selectAgentClass: "请选择智能体类",
                            selectAgentTypeFirst: "请先选择智能体类型",
                            pleaseSelectAgentType: "请选择智能体类型",
                            pleaseSelectAgentClass: "请选择智能体类",
                            selectAgentTypeAndClass: "请先选择智能体类型和智能体类",
                            agentTypes: {
                                citizen: "市民",
                                supervisor: "监管者"
                            },
                            profileSection: "画像配置",
                            baseSection: "基础配置",
                            agentConfig: "智能体配置",
                            blockConfig: "Block 配置",
                            selectBlocks: "选择 Blocks",
                            selectBlocksPlaceholder: "选择要配置的 blocks",
                            choiceWeights: "选项权重",
                            choiceWeightsTooltip: "权重之和应为1",
                            required: "必填",
                            option: "选项",
                            weight: "权重",
                            distributionType: "分布类型",
                            uniformDistribution: "均匀分布",
                            normalDistribution: "正态分布",
                            minValue: "最小值",
                            maxValue: "最大值",
                            mean: "均值",
                            standardDeviation: "标准差",
                            discreteChoice: "离散选择",
                            baseLocation: "基础位置",
                            homeAreaId: "家庭区域ID",
                            workAreaId: "工作区域ID",
                            messages: {
                                createSuccess: "模板创建成功",
                                createFailed: "模板创建失败",
                                updateSuccess: "模板更新成功",
                                updateFailed: "模板更新失败",
                                fetchFailed: "获取模板失败"
                            }
                        },
                        workflow: {
                            title: "工作流配置",
                            createNew: "新建",
                            searchPlaceholder: "搜索工作流",
                            editTitle: "编辑工作流",
                            createTitle: "创建工作流",
                            settingsTitle: "工作流设置",
                            deleteConfirm: "确定要删除这个工作流吗？",
                            workflowSteps: "工作流步骤",
                            defaultRunDescription: "运行1天的模拟",
                            step: "步骤",
                            stepTooltip: "运行单步模拟",
                            stepType: "步骤类型",
                            pleaseSelectStepType: "请选择步骤类型",
                            selectStepType: "选择步骤类型",
                            run: "运行",
                            runTooltip: "运行典型日模拟",
                            environmentIntervene: "环境干预",
                            environmentInterveneTooltip: "更新环境信息，例如天气信息等",
                            nextRound: "下一轮",
                            nextRoundTooltip: "重置智能体并准备启动下一轮模拟",
                            function: "函数",
                            functionTooltip: "运行特定功能函数",
                            functionName: "函数名称",
                            functionNameTooltip: "选择要运行的函数",
                            pleaseSelectFunction: "请选择函数",
                            selectFunction: "选择函数",
                            days: "天数",
                            pleaseEnterDays: "请输入天数",
                            daysTooltip: "此步骤持续的天数",
                            ticksPerStep: "每步时钟数",
                            ticksPerStepTooltip: "环境中每步的时钟数",
                            steps: "步数",
                            pleaseEnterSteps: "请输入步数",
                            stepsTooltip: "此步骤持续的步数",
                            environmentKey: "环境键",
                            pleaseEnterEnvironmentKey: "请输入环境键",
                            environmentKeyTooltip: "环境干预的键标识符",
                            enterEnvironmentKey: "输入环境键",
                            environmentValue: "环境值",
                            pleaseEnterEnvironmentValue: "请输入环境值",
                            environmentValueTooltip: "要设置的环境键值",
                            enterEnvironmentValue: "输入环境值",
                            description: "描述",
                            descriptionTooltip: "此工作流步骤的描述",
                            enterStepDescription: "输入步骤描述",
                            addWorkflowStep: "添加工作流步骤",
                            messages: {
                                loadFailed: "加载工作流失败",
                                deleteSuccess: "工作流删除成功",
                                deleteFailed: "删除工作流失败",
                                updateSuccess: "工作流更新成功",
                                updateFailed: "更新工作流失败",
                                createSuccess: "工作流创建成功",
                                createFailed: "创建工作流失败"
                            }
                        },
                        profile: {
                            title: "智能体画像",
                            createNew: "新建",
                            searchPlaceholder: "搜索画像",
                            editTitle: "编辑画像",
                            createTitle: "创建画像",
                            metadataTitle: "画像元数据",
                            settingsTitle: "画像设置",
                            basicInfo: "基本信息",
                            namePlaceholder: "请输入画像名称",
                            descriptionPlaceholder: "请输入画像描述",
                            personality: "性格特征",
                            personalityPlaceholder: "请输入性格特征",
                            background: "背景故事",
                            backgroundPlaceholder: "请输入背景故事",
                            preferences: "偏好设置",
                            preferencesPlaceholder: "请输入偏好设置",
                            uploadProfile: "上传画像",
                            uploadTitle: "上传画像",
                            uploadHint: "点击或拖拽文件到此区域上传",
                            uploadDescription: "支持JSON文件。文件应包含智能体画像数据。",
                            enterDescription: "请输入此画像的描述",
                            cancel: "取消",
                            upload: "上传",
                            pleaseSelectFile: "请选择要上传的文件",
                            messages: {
                                loadFailed: "加载画像失败",
                                deleteSuccess: "画像删除成功",
                                deleteFailed: "删除画像失败",
                                updateSuccess: "画像更新成功",
                                updateFailed: "更新画像失败",
                                createSuccess: "画像创建成功",
                                createFailed: "创建画像失败",
                                uploadSuccess: "画像上传成功",
                                uploadFailed: "上传画像失败",
                                noData: "没有可下载的数据",
                                deleteConfirm: "确定要删除这个画像吗？"
                            },
                            table: {
                                name: "名称",
                                description: "描述",
                                count: "数量",
                                createdAt: "创建时间",
                                actions: "操作",
                                download: "下载",
                                delete: "删除"
                            }
                        }
                    }
                }
            }
        }
    });

export default i18n;