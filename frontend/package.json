{"name": "agentsociety-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@ant-design/pro-components": "^2.8.3", "@ant-design/x": "^1.0.3", "@monaco-editor/react": "^4.7.0", "@types/react-plotly.js": "^2.6.3", "antd": "^5.22.5", "casdoor-js-sdk": "^0.16.0", "casdoor-react-sdk": "^1.2.0", "dayjs": "^1.11.13", "deck.gl": "^9.0.38", "i18next": "^25.0.1", "i18next-browser-languagedetector": "^8.0.5", "jwt-decode": "^4.0.0", "mapbox-gl": "^3.9.0", "mobx": "^6.13.5", "mobx-react-lite": "^4.1.0", "plotly.js": "^3.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.4.1", "react-map-gl": "^7.1.7", "react-plotly.js": "^2.6.0", "react-router-dom": "^7.0.2", "survey-creator-react": "^1.12.15", "tinycolor2": "^1.6.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}}