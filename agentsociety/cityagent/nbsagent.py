from typing import Any, Optional

import numpy as np
from pydantic import Field

from ..agent import AgentParams, AgentToolbox, NBSAgentBase
from ..logger import get_logger
from ..memory import Memory
from ..agent.block import Block

__all__ = ["NBSAgent"]


class NBSAgentConfig(AgentParams):
    """Configuration for NBSAgent."""

    time_diff: int = Field(
        default=30 * 24 * 60 * 60,
        description="Time difference between each forward, day * hour * minute * second",
    )
    num_labor_hours: int = Field(
        default=168, description="Number of labor hours per week"
    )
    productivity_per_labor: float = Field(
        default=1, description="Productivity per labor hour"
    )


class NBSAgent(NBSAgentBase):
    """National Bureau of Statistics Agent simulating economic data collection and analysis.

    Inherits from InstitutionAgent to manage economic indicators and interactions with other
    agents in a simulated environment. Handles monthly economic metrics calculations including
    GDP, labor statistics, prices, and citizen welfare indicators.
    """

    ParamsType = NBSAgentConfig
    description: str = """
The National Bureau of Statistics Agent simulating economic data collection and analysis.
    """

    def __init__(
        self,
        id: int,
        name: str,
        toolbox: AgentToolbox,
        memory: Memory,
        agent_params: Optional[NBSAgentConfig] = None,
        blocks: Optional[list[Block]] = None,
    ) -> None:
        """Initialize NBSAgent with dependencies and configuration.

        - **Args**:
            - `name` (`str`): The name or identifier of the agent.
            - `toolbox` (`AgentToolbox`): The toolbox of the agent.
            - `memory` (`Memory`): The memory of the agent.

        - **Description**:
            - Initializes the NBSAgent with the provided parameters and sets up necessary internal states.
        """
        super().__init__(
            id=id,
            name=name,
            toolbox=toolbox,
            memory=memory,
            agent_params=agent_params,
            blocks=blocks,
        )
        self.initailzed = False
        self.last_time_trigger = None
        self.forward_times = 0

    async def reset(self):
        """Reset the NBSAgent."""
        pass

    async def month_trigger(self):
        """Check if a monthly cycle should be triggered based on simulation time.

        Returns:
            True if monthly interval has passed since last trigger, False otherwise
        """
        now_tick = self.environment.get_tick()
        if self.last_time_trigger is None:
            self.last_time_trigger = now_tick
            return False
        if now_tick - self.last_time_trigger >= self.params.time_diff:
            self.last_time_trigger = now_tick
            return True
        return False

    async def forward(self):
        """Execute monthly economic data collection and update cycle.

        Performs:
        1. Real GDP calculation
        2. Labor statistics aggregation
        3. Price level monitoring
        4. Citizen welfare metrics collection
        5. Economic indicator updates
        """
        if await self.month_trigger():
            # TODO: fix bug here, what is the t_now ??
            get_logger().debug(f"Agent {self.id}: Start main workflow - nbs forward")
            t_now = str(self.environment.get_tick())
            nbs_id = self.id
            await self.environment.economy_client.calculate_real_gdp(nbs_id)
            citizens_ids = await self.memory.status.get("citizen_ids")
            # TODO: move gather_messages to simulator
            work_propensity = await self.gather_messages(
                citizens_ids, "work_propensity"
            )
            if sum(work_propensity) == 0.0:
                working_hours = 0.0
            else:
                working_hours = np.mean(work_propensity) * self.params.num_labor_hours
            await self.environment.economy_client.update(
                nbs_id, "working_hours", {t_now: working_hours}, mode="merge"
            )
            firms_id = await self.environment.economy_client.get_firm_ids()
            prices = await self.environment.economy_client.get(firms_id, "price")

            await self.environment.economy_client.update(
                nbs_id, "prices", {t_now: float(np.mean(prices))}, mode="merge"
            )
            # TODO: move gather_messages to simulator
            depression = await self.gather_messages(citizens_ids, "depression")
            if sum(depression) == 0.0:
                depression = 0.0
            else:
                depression = np.mean(depression)
            await self.environment.economy_client.update(
                nbs_id, "depression", {t_now: depression}, mode="merge"
            )
            consumption_currency = await self.environment.economy_client.get(
                citizens_ids, "consumption"
            )
            if sum(consumption_currency) == 0.0:
                consumption_currency = 0.0
            else:
                consumption_currency = np.mean(consumption_currency)
            await self.environment.economy_client.update(
                nbs_id,
                "consumption_currency",
                {t_now: consumption_currency},
                mode="merge",
            )
            income_currency = await self.environment.economy_client.get(
                citizens_ids, "income"
            )
            if sum(income_currency) == 0.0:
                income_currency = 0.0
            else:
                income_currency = np.mean(income_currency)
            await self.environment.economy_client.update(
                nbs_id, "income_currency", {t_now: income_currency}, mode="merge"
            )
            get_logger().debug(f"Agent {self.id}: Finished main workflow - nbs forward")
            self.forward_times += 1
            await self.memory.status.update("forward_times", self.forward_times)
