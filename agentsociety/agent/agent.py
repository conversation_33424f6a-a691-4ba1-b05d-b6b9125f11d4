from __future__ import annotations

import random
from datetime import datetime, timezone
from typing import Any, Optional

import jsonc
from pycityproto.city.person.v2 import person_pb2 as person_pb2

from ..environment.sim.person_service import PersonService
from ..logger import get_logger
from ..memory import Memory
from ..message import Message
from ..storage import StorageDialog, StorageDialogType, StorageSurvey
from ..survey.models import Survey
from .agent_base import Agent, AgentToolbox, AgentType, extract_json
from .block import Block
from .decorator import register_get

__all__ = [
    "CitizenAgentBase",
    "FirmAgentBase",
    "BankAgentBase",
    "NBSAgentBase",
    "GovernmentAgentBase",
]


class CitizenAgentBase(Agent):
    """
    Represents a citizen agent within the simulation environment.

    - **Description**:
        - This class extends the base `Agent` class and is designed to simulate the behavior of a city resident.
        - It includes initialization of various clients (like LLM, economy) and services required for the agent's operation.
        - Provides methods for binding the agent to the simulator and economy system, as well as handling specific types of messages.

    - **Attributes**:
        - `_mlflow_client`: An optional client for integrating with MLflow for experiment tracking and management.
    """

    def __init__(
        self,
        id: int,
        name: str,
        toolbox: AgentToolbox,
        memory: Memory,
        agent_params: Optional[Any] = None,
        blocks: Optional[list[Block]] = None,
    ) -> None:
        """
        Initialize a new instance of the CitizenAgent.

        - **Args**:
            - `id` (`int`): The ID of the agent.
            - `name` (`str`): The name or identifier of the agent.
            - `toolbox` (`AgentToolbox`): The toolbox of the agent.
            - `memory` (`Memory`): The memory of the agent.

        - **Description**:
            - Initializes the CitizenAgent with the provided parameters and sets up necessary internal states.
        """
        super().__init__(
            id=id,
            name=name,
            type=AgentType.Citizen,
            toolbox=toolbox,
            memory=memory,
            agent_params=agent_params,
            blocks=blocks,
        )

    async def init(self):
        """
        Initialize the agent.

        - **Description**:
            - Calls the `_bind_to_simulator` method to establish the agent within the simulation environment.
            - Calls the `_bind_to_economy` method to integrate the agent into the economy simulator.
        """
        await super().init()
        await self._bind_to_simulator()
        await self._bind_to_economy()

    async def _bind_to_simulator(self):
        """
        Bind the agent to the Traffic Simulator.

        - **Description**:
            - If the simulator is set, this method binds the agent by creating a person entity in the simulator based on the agent's attributes.
            - Updates the agent's status with the newly created person ID from the simulator.
            - Logs the successful binding to the person entity added to the simulator.
        """
        FROM_MEMORY_KEYS = {
            "attribute",
            "home",
            "work",
            "vehicle_attribute",
            "bus_attribute",
            "pedestrian_attribute",
            "bike_attribute",
        }
        simulator = self.environment
        status = self.status
        dict_person = PersonService.default_person(return_dict=True)
        dict_person["id"] = self.id
        for _key in FROM_MEMORY_KEYS:
            try:
                _value = await status.get(_key)
                if _value:
                    dict_person[_key] = _value
            except KeyError as e:
                continue
        await simulator.add_person(dict_person)

    async def _bind_to_economy(self):
        """
        Bind the agent to the Economy Simulator.
        """
        person_id = await self.status.get("id")
        currency = await self.status.get("currency")
        skill = await self.status.get("work_skill", 0.0)
        consumption = 0.0
        income = 0.0
        await self.environment.economy_client.add_agents(
            {
                "id": person_id,
                "currency": currency,
                "skill": skill,
                "consumption": consumption,
                "income": income,
            }
        )

    async def update_motion(self):
        """
        Update the motion of the agent. Usually used in the starting of the `forward` method.
        """
        resp = await self.environment.get_person(self.id)
        resp_dict = resp["person"]
        for k, v in resp_dict.get("motion", {}).items():
            try:
                await self.status.get(k)
                await self.status.update(
                    k, v, mode="replace", protect_llm_read_only_fields=False
                )
            except KeyError as e:
                get_logger().debug(
                    f"KeyError: {e} when updating motion of agent {self.id}"
                )
                continue

    async def do_survey(self, survey: Survey) -> str:
        """
        Generate a response to a user survey based on the agent's memory and current state.

        - **Args**:
            - `survey` (`Survey`): The survey that needs to be answered.

        - **Returns**:
            - `str`: The generated response from the agent.

        - **Description**:
            - Prepares a prompt for the Language Model (LLM) based on the provided survey.
            - Constructs a dialog including system prompts, relevant memory context, and the survey question itself.
            - Uses the LLM client to generate a response asynchronously.
            - If the LLM client is not available, it returns a default message indicating unavailability.
            - This method can be overridden by subclasses to customize survey response generation.
        """
        survey_prompt = survey.to_prompt()
        dialog = []

        # Add system prompt
        system_prompt = "Please answer the survey question in first person. Follow the format requirements strictly and provide clear and specific answers (In JSON format)."
        dialog.append({"role": "system", "content": system_prompt})

        # Add memory context
        if self.memory:
            profile_and_states = await self.status.search(survey_prompt)
            relevant_activities = await self.stream.search(survey_prompt)

            dialog.append(
                {
                    "role": "system",
                    "content": f"Answer based on following profile and states:\n{profile_and_states}\n Related activities:\n{relevant_activities}",
                }
            )

        # Add survey question
        dialog.append({"role": "user", "content": survey_prompt})

        for retry in range(10):
            try:
                # Use LLM to generate a response
                # print(f"dialog: {dialog}")
                _response = await self.llm.atext_request(
                    dialog, response_format={"type": "json_object"}
                )
                # print(f"response: {_response}")
                json_str = extract_json(_response)
                if json_str:
                    json_dict = jsonc.loads(json_str)
                    json_str = jsonc.dumps(json_dict, ensure_ascii=False)
                    break
            except:
                pass
        else:
            import traceback

            traceback.print_exc()
            get_logger().error("Failed to generate survey response")
            json_str = ""
        return json_str

    async def _handle_survey_with_storage(
        self,
        survey: Survey,
        survey_day: Optional[int] = None,
        survey_t: Optional[float] = None,
        is_pending_survey: bool = False,
        pending_survey_id: Optional[int] = None,
    ) -> str:
        """
        Process a survey by generating a response and recording it in Avro format and PostgreSQL.

        - **Args**:
            - `survey` (`Survey`): The survey data that includes an ID and other relevant information.
            - `survey_day` (`Optional[int]`): The day of the survey.
            - `survey_t` (`Optional[float]`): The time of the survey.
            - `is_pending_survey` (`bool`): Whether the survey is a pending survey.
            - `pending_survey_id` (`Optional[int]`): The ID of the pending survey.

        - **Description**:
            - Generates a survey response using `generate_user_survey_response`.
            - Records the response with metadata (such as timestamp, survey ID, etc.) in Avro format and appends it to an Avro file if `_avro_file` is set.
            - Writes the response and metadata into a PostgreSQL database asynchronously through `_pgsql_writer`, ensuring any previous write operation has completed.
            - Sends a message through the Messager indicating user feedback has been processed.
            - Handles asynchronous tasks and ensures thread-safe operations when writing to PostgreSQL.
        """
        survey_response = await self.do_survey(survey)
        date_time = datetime.now(timezone.utc)
        # Avro
        day, t = self.environment.get_datetime()
        storage_survey = StorageSurvey(
            id=self.id,
            day=survey_day if survey_day is not None else day,
            t=survey_t if survey_t is not None else t,
            survey_id=str(survey.id),
            result=survey_response,
            created_at=date_time,
        )
        if self.avro_saver is not None:
            self.avro_saver.append_surveys([storage_survey])
        # Pg
        if self.pgsql_writer is not None:
            if self._last_asyncio_pg_task is not None:
                await self._last_asyncio_pg_task
                self._last_asyncio_pg_task = None
            if is_pending_survey:
                await self.pgsql_writer.write_surveys.remote(  # type:ignore
                    [storage_survey]
                )
                self._last_asyncio_pg_task = (
                    self.pgsql_writer.mark_surveys_as_processed.remote(  # type:ignore
                        [pending_survey_id]
                    )
                )
            else:
                self._last_asyncio_pg_task = (
                    self.pgsql_writer.write_surveys.remote(  # type:ignore
                        [storage_survey]
                    )
                )
        # status memory
        old_survey_responses = await self.memory.status.get("survey_responses", [])
        new_survey_responses = old_survey_responses + [survey_response]
        await self.memory.status.update(
            "survey_responses",
            new_survey_responses,
            protect_llm_read_only_fields=False,
        )
        return survey_response

    async def do_interview(self, question: str) -> str:
        """
        Generate a response to a user's chat question based on the agent's memory and current state.

        - **Args**:
            - `question` (`str`): The question that needs to be answered.

        - **Returns**:
            - `str`: The generated response from the agent.

        - **Description**:
            - Prepares a prompt for the Language Model (LLM) with a system prompt to guide the response style.
            - Constructs a dialog including relevant memory context and the user's question.
            - Uses the LLM client to generate a concise and clear response asynchronously.
            - If the LLM client is not available, it returns a default message indicating unavailability.
            - This method can be overridden by subclasses to customize chat response generation.
        """
        dialog = []

        # Add system prompt
        system_prompt = "Please answer the question in first person and keep the response concise and clear."
        dialog.append({"role": "system", "content": system_prompt})

        # Add memory context
        if self._memory:
            profile_and_states = await self.status.search(question, top_k=10)
            relevant_activities = await self.stream.search(question, top_k=10)

            dialog.append(
                {
                    "role": "system",
                    "content": f"Answer based on following profile and states:\n{profile_and_states}\n Related activities:\n{relevant_activities}",
                }
            )

        # Add user question
        dialog.append({"role": "user", "content": question})

        # Use LLM to generate a response
        response = await self.llm.atext_request(dialog)

        return response

    async def _handle_interview_with_storage(self, message: Message) -> str:
        """
        Process an interview interaction by generating a response and recording it in Avro format and PostgreSQL.

        - **Args**:
            - `question` (`str`): The interview data containing the content of the user's message.
        """
        question = message.payload["content"]
        day, t = self.environment.get_datetime()
        storage_dialog = StorageDialog(
            id=self.id,
            day=message.day,
            t=message.t,
            type=StorageDialogType.User,
            speaker="user",
            content=question,
            created_at=datetime.now(timezone.utc),
        )
        if self.avro_saver is not None:
            self.avro_saver.append_dialogs([storage_dialog])
        if self.pgsql_writer is not None:
            if self._last_asyncio_pg_task is not None:
                await self._last_asyncio_pg_task
            self._last_asyncio_pg_task = (
                self.pgsql_writer.write_dialogs.remote(  # type:ignore
                    [storage_dialog]
                )
            )
        response = await self.do_interview(question)
        storage_dialog = StorageDialog(
            id=self.id,
            day=day,
            t=t,
            type=StorageDialogType.User,
            speaker="",
            content=response,
            created_at=datetime.now(timezone.utc),
        )
        # Avro
        if self.avro_saver is not None:
            self.avro_saver.append_dialogs([storage_dialog])
        # Pg
        if self.pgsql_writer is not None:
            if self._last_asyncio_pg_task is not None:
                await self._last_asyncio_pg_task
                self._last_asyncio_pg_task = None
            await self.pgsql_writer.write_dialogs.remote(  # type:ignore
                [storage_dialog]
            )
            if message.extra is not None and "pending_dialog_id" in message.extra:
                self._last_asyncio_pg_task = (
                    self.pgsql_writer.mark_dialogs_as_processed.remote(  # type:ignore
                        [message.extra["pending_dialog_id"]]
                    )
                )
        return response

    async def save_agent_thought(self, thought: str):
        """
        Save the agent's thought to the memory.

        - **Args**:
            - `thought` (`str`): The thought data to be saved.

        - **Description**:
            - Saves the thought data to the memory.
        """
        day, t = self.environment.get_datetime()
        await self.memory.stream.add_cognition(thought)
        storage_thought = StorageDialog(
            id=self.id,
            day=day,
            t=t,
            type=StorageDialogType.Thought,
            speaker="",
            content=thought,
            created_at=datetime.now(timezone.utc),
        )
        # Avro
        if self.avro_saver is not None:
            self.avro_saver.append_dialogs([storage_thought])
        # Pg
        if self.pgsql_writer is not None:
            if self._last_asyncio_pg_task is not None:
                await self._last_asyncio_pg_task
            self._last_asyncio_pg_task = (
                self.pgsql_writer.write_dialogs.remote(  # type:ignore
                    [storage_thought]
                )
            )

    async def do_chat(self, message: Message) -> str:
        """
        Process a chat message received from another agent and record it.

        - **Args**:
            - `message` (`Message`): The chat message data received from another agent.
        """
        resp = f"Agent {self.id} received agent chat response: {message.payload}"
        get_logger().debug(resp)
        return resp

    async def _handle_agent_chat_with_storage(self, message: Message):
        """
        Process a chat message received from another agent and record it.

        - **Args**:
            - `payload` (`dict`): The chat message data received from another agent.

        - **Description**:
            - Logs the incoming chat message from another agent.
            - Prepares the chat message for storage in Avro format and PostgreSQL.
            - Writes the chat message and metadata into an Avro file if `_avro_file` is set.
            - Ensures thread-safe operations when writing to PostgreSQL by waiting for any previous write task to complete before starting a new one.
        """
        try:
            content = jsonc.dumps(message.payload, ensure_ascii=False)
        except:
            content = str(message.payload)
        storage_dialog = StorageDialog(
            id=self.id,
            day=message.day,
            t=message.t,
            type=StorageDialogType.Talk,
            speaker=str(message.from_id),
            content=content,
            created_at=datetime.now(timezone.utc),
        )
        await self.do_chat(message)
        # Avro
        if self.avro_saver is not None:
            self.avro_saver.append_dialogs([storage_dialog])
        # Pg
        if self.pgsql_writer is not None:
            if self._last_asyncio_pg_task is not None:
                await self._last_asyncio_pg_task
            self._last_asyncio_pg_task = (
                self.pgsql_writer.write_dialogs.remote(  # type:ignore
                    [storage_dialog]
                )
            )

    async def get_aoi_info(self):
        """Get the surrounding environment information - aoi information"""
        position = await self.status.get("position")
        if "aoi_position" in position:
            parent_id = position["aoi_position"]["aoi_id"]
            return self.environment.sense_aoi(parent_id)
        else:
            return None

    @register_get("Get the current time in the format of HH:MM:SS")
    async def get_nowtime(self):
        """Get the current time"""
        now_time = self.environment.get_datetime(format_time=True)
        return now_time[1]

    async def before_forward(self):
        """
        Before forward.
        """
        await super().before_forward()
        # sync agent status with simulator
        await self.update_motion()
        get_logger().debug(f"Agent {self.id}: Finished main workflow - update motion")


class InstitutionAgentBase(Agent):
    """
    Represents an institution agent within the simulation environment.

    - **Description**:
        - This class extends the base `Agent` class and is designed to simulate the behavior of an institution, such as a bank, government body, or corporation.
        - It includes initialization of various clients (like LLM, economy) and services required for the agent's operation.
        - Provides methods for binding the agent to the economy system and handling specific types of messages, like gathering information from other agents.

    - **Attributes**:
        - `_mlflow_client`: An optional client for integrating with MLflow for experiment tracking and management.
        - `_gather_responses`: A dictionary mapping agent IDs to `asyncio.Future` objects used for collecting responses to gather requests.
    """

    def __init__(
        self,
        id: int,
        name: str,
        toolbox: AgentToolbox,
        memory: Memory,
        agent_params: Optional[Any] = None,
        blocks: Optional[list[Block]] = None,
    ):
        """
        Initialize a new instance of the InstitutionAgent.

        - **Args**:
            - `name` (`str`): The name or identifier of the agent.
            - `toolbox` (`AgentToolbox`): The toolbox of the agent.
            - `memory` (`Memory`): The memory of the agent.

        - **Description**:
            - Initializes the InstitutionAgent with the provided parameters and sets up necessary internal states.
            - Adds a response collector (`_gather_responses`) for handling responses to gather requests.
        """
        super().__init__(
            id=id,
            name=name,
            type=AgentType.Institution,
            toolbox=toolbox,
            memory=memory,
            agent_params=agent_params,
            blocks=blocks,
        )

    async def init(self):
        """
        Initialize the agent.

        - **Description**:
            - Calls the `_bind_to_economy` method to integrate the agent into the economy simulator.
        """
        await super().init()
        await self._bind_to_economy()

    async def _bind_to_economy(self):
        """
        Bind the agent to the Economy Simulator.

        - **Description**:
            - Calls the `_bind_to_economy` method to integrate the agent into the economy system.
            - Note that this method does not bind the agent to the simulator itself; it only handles the economy integration.
        """
        map_header: dict = self.environment.map.get_map_header()
        # TODO: remove random position assignment
        await self.status.update(
            "position",
            {
                "xy_position": {
                    "x": float(
                        random.randrange(
                            start=int(map_header["west"]),
                            stop=int(map_header["east"]),
                        )
                    ),
                    "y": float(
                        random.randrange(
                            start=int(map_header["south"]),
                            stop=int(map_header["north"]),
                        )
                    ),
                }
            },
            protect_llm_read_only_fields=False,
        )
        _type = None
        _status = self.status
        _id = await _status.get("id")
        _type = await _status.get("type")
        nominal_gdp = await _status.get("nominal_gdp", [])
        real_gdp = await _status.get("real_gdp", [])
        unemployment = await _status.get("unemployment", [])
        wages = await _status.get("wages", [])
        prices = await _status.get("prices", [])
        inventory = await _status.get("inventory", 0)
        price = await _status.get("price", 0)
        currency = await _status.get("currency", 0.0)
        interest_rate = await _status.get("interest_rate", 0.0)
        bracket_cutoffs = await _status.get("bracket_cutoffs", [])
        bracket_rates = await _status.get("bracket_rates", [])
        consumption_currency = await _status.get("consumption_currency", [])
        consumption_propensity = await _status.get("consumption_propensity", [])
        income_currency = await _status.get("income_currency", [])
        depression = await _status.get("depression", [])
        locus_control = await _status.get("locus_control", [])
        working_hours = await _status.get("working_hours", [])
        employees = await _status.get("employees", [])
        citizens = await _status.get("citizens", [])
        demand = await _status.get("demand", 0)
        sales = await _status.get("sales", 0)
        await self.environment.economy_client.add_orgs(
            {
                "id": _id,
                "type": _type,
                "nominal_gdp": nominal_gdp,
                "real_gdp": real_gdp,
                "unemployment": unemployment,
                "wages": wages,
                "prices": prices,
                "inventory": inventory,
                "price": price,
                "currency": currency,
                "interest_rate": interest_rate,
                "bracket_cutoffs": bracket_cutoffs,
                "bracket_rates": bracket_rates,
                "consumption_currency": consumption_currency,
                "consumption_propensity": consumption_propensity,
                "income_currency": income_currency,
                "depression": depression,
                "locus_control": locus_control,
                "working_hours": working_hours,
                "employees": employees,
                "citizens": citizens,
                "demand": demand,
                "sales": sales,
            }
        )

    async def react_to_intervention(self, intervention_message: str):
        """
        React to an intervention.

        - **Args**:
            - `intervention_message` (`str`): The message of the intervention.

        - **Description**:
            - React to an intervention.
        """
        ...


class FirmAgentBase(InstitutionAgentBase):
    """
    Represents a firm agent within the simulation environment.
    """


class BankAgentBase(InstitutionAgentBase):
    """
    Represents a bank agent within the simulation environment.
    """

    ...


class NBSAgentBase(InstitutionAgentBase):
    """
    Represents a National Bureau of Statistics agent within the simulation environment.
    """

    ...


class GovernmentAgentBase(InstitutionAgentBase):
    """
    Represents a government agent within the simulation environment.
    """

    ...


class SupervisorBase(Agent):
    def __init__(
        self,
        id: int,
        name: str,
        toolbox: AgentToolbox,
        memory: Memory,
        agent_params: Optional[Any] = None,
        blocks: Optional[list[Block]] = None,
    ) -> None:
        """
        Initialize a new instance of the CitizenAgent.

        - **Args**:
            - `id` (`int`): The ID of the agent.
            - `name` (`str`): The name or identifier of the agent.
            - `toolbox` (`AgentToolbox`): The toolbox of the agent.
            - `memory` (`Memory`): The memory of the agent.

        - **Description**:
            - Initializes the CitizenAgent with the provided parameters and sets up necessary internal states.
        """
        super().__init__(
            id=id,
            name=name,
            type=AgentType.Supervisor,
            toolbox=toolbox,
            memory=memory,
            agent_params=agent_params,
            blocks=blocks,
        )

    async def reset(self):
        """
        Reset the agent.
        """
        pass

    async def react_to_intervention(self, intervention_message: str):
        """
        React to an intervention.
        """
        pass

    async def forward(
        self,
        current_round_messages: list[Message],
    ) -> tuple[
        dict[Message, bool],
        list[Message],
    ]:
        """
        Process and validate messages from the current round, performing validation and intervention

        Args:
            current_round_messages: List of messages for the current round, each element is a tuple of (sender_id, receiver_id, content)

        Returns:
            validation_dict: Dictionary of message validation results, key is message tuple, value is whether validation passed
            persuasion_messages: List of persuasion messages
        """
        raise NotImplementedError(
            "This method `forward` should be implemented by the subclass"
        )
