# Customize Agents

This document provides a detailed guide on how to customize the behavior logic of agents in our simulation environment. The behavior of agents is controlled by the `forward` method, which is triggered in a step-by-step manner, synchronized with the simulation environment's second-by-second update logic.

```{toctree}
:maxdepth: 2

01-concept
02-agent-tools
03-memory
04-prompt-organization
05-agent-customization
```
