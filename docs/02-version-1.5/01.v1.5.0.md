# Changelog: Version 1.5.0

## ❗Breaking Changes
- [Agent system architecture](https://github.com/tsinghua-fib-lab/agentsociety/tree/v1.5.0/agentsociety/agent) significantly modified
- [Configuration system](https://github.com/tsinghua-fib-lab/agentsociety/tree/v1.5.0/agentsociety/configs) significantly modified

## 🎉 Major Changes

### 1. The Agent-Block-Action Architecture
The `Agent-Block-Action` architecture is a new architecture for the agent system. It is designed to be more flexible and modular. 

The new architecture includes:
- `Agent`: The central entity in the simulation, capable of perception, reasoning, communication, and action.
- `Block`: A foundational component that encapsulates modular functionality, idealy represents a specific functional behavior.
- `Action`: Represents specific functional behaviors that a Block provides (e.g., movement, location selection).

Refer to [agent-block-action architecture](./02.agent-block-action architecture.md) for more details.

### 2. Enhanced Workflow Configuration
- Added more types of workflow steps
  - `NEXT_ROUND`: Proceed to the next round of the simulation —— reset agents but keep the memory. The reset logic is controlled by the [`reset` function in the `Agent` class](https://github.com/tsinghua-fib-lab/agentsociety/tree/v1.5.0/agentsociety/agent/agent_base.py).
  - `DELETE_AGENT`: Delete the specified agents when you don't need them anymore.
- [Filter Configuration](https://github.com/tsinghua-fib-lab/agentsociety/tree/v1.5.0/agentsociety/configs/exp.py) for those steps that needs to specify the target agents.

### 3. Enhanced Agent Configuration
- Added `agent_params` field in the `AgentConfig` to modify the working behavior of target agents.
- Added `blocks` field in the `AgentConfig` to endow the agent with more capabilities.

### 4. Enhanced FormatPrompt
The [FormatPrompt](https://github.com/tsinghua-fib-lab/agentsociety/tree/v1.5.0/agentsociety/agent/prompt.py) is enhanced to support different kinds of prompt formation:
- **Flexible Template System**: Supports both simple variable substitution and complex expression evaluation with automatic async handling.
- **Multi-source Variable Resolution**: Intelligently retrieves values from multiple sources with clear precedence rules.
- **Secure Expression Evaluation**: Implements robust safety checks to prevent dangerous operations while supporting powerful expressions.

Refer to [FormatPrompt](../05-custom-agents/04-prompt-organization.md) for more details.

### 5. Share your design with others
We build the [agentsociety-community](https://github.com/tsinghua-fib-lab/agentsociety-community) repository to log and share your design with others. The repository not only contains official design examples, but also welcomes community contributions.
- **Agent Design Sharing**: Share your agent design with others.
- **Block Design Sharing**: Share your block design with others.

To share your design, you must provide more context for your agent or block. **More details will come soon.**

## Deprecations
- Removed `param_config` parameter in the `AgentConfig`. Use `agent_params` instead - each agent class has its own parameter configurations. Use this to modify the working behavior of target agents.
- Removed `total_tick` parameter in the `EnvironmentConfig`.
