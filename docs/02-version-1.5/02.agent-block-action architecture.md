# Agent-Block-Action Architecture

This document explains the Agent-Block-Action architecture design pattern used in the agentsociety framework.

## Architecture Overview

The Agent-Block-Action architecture is a modular design pattern that enables flexible, extensible agent development. It consists of three main components:

- **Agent**: Contains all the fundamental logic and functionality required for an intelligent agent's operation.
- **Block**: Serves as a plugin that provides additional capabilities to an agent (e.g., MobilityBlock enables agents to move within a city).
- **Action**: Represents specific functional behaviors that a Block provides (e.g., movement, location selection).

## Agent

Agents are the central entities in the simulation, capable of perception, reasoning, communication, and action.

### Agent Definition

When creating a custom agent, you need to define:

1. **Agent Parameters (agent_params)**: Configuration settings that control the agent's behavior.
   ```python
   class AgentParams(BaseModel):
       block_dispatch_prompt: str = Field(default=DISPATCHER_PROMPT, 
           description="The prompt used for the block dispatcher...")
   ```

2. **Blocks**: Optional modular components that extend the agent's capabilities.
   ```python
   def __init__(
       self,
       id: int,
       name: str,
       type: AgentType,
       toolbox: AgentToolbox,
       memory: Memory,
       agent_params: Optional[Any] = None,
       blocks: Optional[list[Block]] = None,
   ):
   ```

3. **Memory Configuration**: Defines the structure of the agent's memory system.
   ```python
   memory_config: dict[str, MemoryT] = {}
   ```

### Block Dispatcher

The BlockDispatcher facilitates intelligent selection and execution of the appropriate blocks based on the agent's current context or intention:

```python
self.dispatcher = BlockDispatcher(self.llm, self.params.block_dispatch_prompt)
if blocks is not None:
    self.blocks = blocks
    self.dispatcher.register_blocks(self.blocks)
```

The specific usage pattern and implementation of the dispatcher are determined by the agent designer in the current version.

### Agent Workflow

The agent's operation follows a structured workflow that executes once per simulation step:

1. **before_forward()**: Preparatory work before the main agent logic runs.
   - Recommended for setup operations and initialization.

2. **before_blocks()**: Executes the before_forward() method of all blocks attached to the agent.
   - Handles preparation for block execution.

3. **forward()**: The core agent logic and decision-making process.
   - Main implementation of agent behavior.

4. **after_blocks()**: Executes the after_forward() method of all blocks attached to the agent.
   - Handles cleanup after block execution.

5. **after_forward()**: Finalizes the agent's operation for the current step.
   - Recommended for cleanup and state management.

```python
async def run(self) -> Any:
    # run required methods before agent forward
    await self.before_forward()
    await self.before_blocks()
    # run agent forward
    await self.forward()
    # run required methods after agent forward
    await self.after_blocks()
    await self.after_forward()
```

## Block and Action

Blocks are modular components that extend agent capabilities, while Actions represent the specific functionalities that blocks provide.

### Block Definition

When creating a custom block, you need to define:

1. **Block Parameters (block_params)**: Configuration settings for the block.
   ```python
   class BlockParams(BaseModel):
       block_memory: Optional[dict[str, Any]] = None
   ```

2. **Actions**: Descriptions of the block's capabilities exposed to agents.
   ```python
   actions: dict[str, str] = {}
   ```

3. **Name and Description**: Metadata that explains the block's purpose.
   ```python
   name: str = ""
   description: str = ""
   ```

### Block Workflow

Each block follows its own workflow:

1. **before_forward()**: Preparation before the main block logic.
   ```python
   async def before_forward(self):
       """
       Before forward
       """
       pass
   ```

2. **forward()**: The core block logic that implements its main functionality.
   ```python
   async def forward(self, *args, **kwargs):
       raise NotImplementedError("Subclasses should implement this method")
   ```

3. **after_forward()**: Finalization and cleanup after block execution.
   ```python
   async def after_forward(self):
       """
       After forward
       """
       pass
   ```

### Block Activation

Blocks are activated by the agent, with the specific timing and method of activation controlled by the agent's implementation. This gives agent designers flexibility in how and when blocks are used:

```python
# In Agent class
async def before_blocks(self):
    for block in self.blocks:
        await block.before_forward()

async def after_blocks(self):
    for block in self.blocks:
        await block.after_forward()
```

## Integration Example

A complete integration might look like:

```python
# Create blocks
mobility_block = MobilityBlock(llm=llm, environment=env, block_params=MobilityBlockParams())
social_block = SocialBlock(llm=llm, environment=env, block_params=SocialBlockParams())

# Create agent with blocks
agent = CitizenAgent(
    id=1,
    name="John",
    type=AgentType.Citizen,
    toolbox=toolbox,
    memory=memory,
    agent_params=CitizenAgentParams(enable_mobility=True),
    blocks=[mobility_block, social_block]
)

# Run agent (called by the simulation engine)
await agent.run()
```

This architecture allows for highly modular, reusable, and extensible agent designs, making it easier to create complex agent behaviors by combining simpler components.
