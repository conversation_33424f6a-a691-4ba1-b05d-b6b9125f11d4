# {py:mod}`agentsociety.storage`

```{py:module} agentsociety.storage
```

```{autodoc2-docstring} agentsociety.storage
:allowtitles:
```

## Submodules

```{toctree}
:titlesonly:
:maxdepth: 1

agentsociety.storage.type
agentsociety.storage.pgsql
agentsociety.storage.avro
```

## Package Contents

### Data

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`__all__ <agentsociety.storage.__all__>`
  - ```{autodoc2-docstring} agentsociety.storage.__all__
    :summary:
    ```
````

### API

````{py:data} __all__
:canonical: agentsociety.storage.__all__
:value: >
   ['AvroSaver', 'AvroConfig', 'PgWriter', 'StorageDialog', 'StorageSurvey', 'StorageDialogType', 'Post...

```{autodoc2-docstring} agentsociety.storage.__all__
```

````
