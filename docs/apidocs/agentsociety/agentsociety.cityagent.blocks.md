# {py:mod}`agentsociety.cityagent.blocks`

```{py:module} agentsociety.cityagent.blocks
```

```{autodoc2-docstring} agentsociety.cityagent.blocks
:allowtitles:
```

## Submodules

```{toctree}
:titlesonly:
:maxdepth: 1

agentsociety.cityagent.blocks.economy_block
agentsociety.cityagent.blocks.mobility_block
agentsociety.cityagent.blocks.cognition_block
agentsociety.cityagent.blocks.other_block
agentsociety.cityagent.blocks.needs_block
agentsociety.cityagent.blocks.utils
agentsociety.cityagent.blocks.plan_block
agentsociety.cityagent.blocks.social_block
```

## Package Contents

### Data

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`__all__ <agentsociety.cityagent.blocks.__all__>`
  - ```{autodoc2-docstring} agentsociety.cityagent.blocks.__all__
    :summary:
    ```
````

### API

````{py:data} __all__
:canonical: agentsociety.cityagent.blocks.__all__
:value: >
   ['MobilityBlock', 'CognitionBlock', 'PlanBlock', 'NeedsBlock', 'SocialBlock', 'EconomyBlock', 'Other...

```{autodoc2-docstring} agentsociety.cityagent.blocks.__all__
```

````
