# {py:mod}`agentsociety.executor`

```{py:module} agentsociety.executor
```

```{autodoc2-docstring} agentsociety.executor
:allowtitles:
```

## Submodules

```{toctree}
:titlesonly:
:maxdepth: 1

agentsociety.executor.process
```

## Package Contents

### Data

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`__all__ <agentsociety.executor.__all__>`
  - ```{autodoc2-docstring} agentsociety.executor.__all__
    :summary:
    ```
````

### API

````{py:data} __all__
:canonical: agentsociety.executor.__all__
:value: >
   ['ProcessExecutor']

```{autodoc2-docstring} agentsociety.executor.__all__
```

````
