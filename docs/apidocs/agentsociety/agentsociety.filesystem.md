# {py:mod}`agentsociety.filesystem`

```{py:module} agentsociety.filesystem
```

```{autodoc2-docstring} agentsociety.filesystem
:allowtitles:
```

## Submodules

```{toctree}
:titlesonly:
:maxdepth: 1

agentsociety.filesystem.client
```

## Package Contents

### Data

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`__all__ <agentsociety.filesystem.__all__>`
  - ```{autodoc2-docstring} agentsociety.filesystem.__all__
    :summary:
    ```
````

### API

````{py:data} __all__
:canonical: agentsociety.filesystem.__all__
:value: >
   ['FileSystemClient']

```{autodoc2-docstring} agentsociety.filesystem.__all__
```

````
