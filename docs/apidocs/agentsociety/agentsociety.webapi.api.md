# {py:mod}`agentsociety.webapi.api`

```{py:module} agentsociety.webapi.api
```

```{autodoc2-docstring} agentsociety.webapi.api
:allowtitles:
```

## Submodules

```{toctree}
:titlesonly:
:maxdepth: 1

agentsociety.webapi.api.config
agentsociety.webapi.api.experiment
agentsociety.webapi.api.survey
agentsociety.webapi.api.mlflow
agentsociety.webapi.api.agent_profiles
agentsociety.webapi.api.agent_template
agentsociety.webapi.api.agent
agentsociety.webapi.api.experiment_runner
agentsociety.webapi.api.const
```

## Package Contents

### Data

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`__all__ <agentsociety.webapi.api.__all__>`
  - ```{autodoc2-docstring} agentsociety.webapi.api.__all__
    :summary:
    ```
* - {py:obj}`api_router <agentsociety.webapi.api.api_router>`
  - ```{autodoc2-docstring} agentsociety.webapi.api.api_router
    :summary:
    ```
````

### API

````{py:data} __all__
:canonical: agentsociety.webapi.api.__all__
:value: >
   ['api_router']

```{autodoc2-docstring} agentsociety.webapi.api.__all__
```

````

````{py:data} api_router
:canonical: agentsociety.webapi.api.api_router
:value: >
   'APIRouter(...)'

```{autodoc2-docstring} agentsociety.webapi.api.api_router
```

````
