# {py:mod}`agentsociety.webapi.api.const`

```{py:module} agentsociety.webapi.api.const
```

```{autodoc2-docstring} agentsociety.webapi.api.const
:allowtitles:
```

## Module Contents

### Data

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`__all__ <agentsociety.webapi.api.const.__all__>`
  - ```{autodoc2-docstring} agentsociety.webapi.api.const.__all__
    :summary:
    ```
* - {py:obj}`DEMO_USER_ID <agentsociety.webapi.api.const.DEMO_USER_ID>`
  - ```{autodoc2-docstring} agentsociety.webapi.api.const.DEMO_USER_ID
    :summary:
    ```
````

### API

````{py:data} __all__
:canonical: agentsociety.webapi.api.const.__all__
:value: >
   ['DEMO_USER_ID']

```{autodoc2-docstring} agentsociety.webapi.api.const.__all__
```

````

````{py:data} DEMO_USER_ID
:canonical: agentsociety.webapi.api.const.DEMO_USER_ID
:value: >
   'DEMO'

```{autodoc2-docstring} agentsociety.webapi.api.const.DEMO_USER_ID
```

````
