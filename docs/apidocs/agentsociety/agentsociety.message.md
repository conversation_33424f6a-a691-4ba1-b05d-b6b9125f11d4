# {py:mod}`agentsociety.message`

```{py:module} agentsociety.message
```

```{autodoc2-docstring} agentsociety.message
:allowtitles:
```

## Submodules

```{toctree}
:titlesonly:
:maxdepth: 1

agentsociety.message.messager
agentsociety.message.message_interceptor
```

## Package Contents

### Data

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`__all__ <agentsociety.message.__all__>`
  - ```{autodoc2-docstring} agentsociety.message.__all__
    :summary:
    ```
````

### API

````{py:data} __all__
:canonical: agentsociety.message.__all__
:value: >
   ['Message', 'Messager', 'MessageKind', 'MessageInterceptor']

```{autodoc2-docstring} agentsociety.message.__all__
```

````
