# {py:mod}`agentsociety.agent`

```{py:module} agentsociety.agent
```

```{autodoc2-docstring} agentsociety.agent
:allowtitles:
```

## Submodules

```{toctree}
:titlesonly:
:maxdepth: 1

agentsociety.agent.prompt
agentsociety.agent.decorator
agentsociety.agent.distribution
agentsociety.agent.context
agentsociety.agent.dispatcher
agentsociety.agent.block
agentsociety.agent.agent_base
agentsociety.agent.memory_config_generator
agentsociety.agent.agent
agentsociety.agent.trigger
```

## Package Contents

### Data

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`__all__ <agentsociety.agent.__all__>`
  - ```{autodoc2-docstring} agentsociety.agent.__all__
    :summary:
    ```
````

### API

````{py:data} __all__
:canonical: agentsociety.agent.__all__
:value: >
   ['Agent', 'AgentParams', 'StatusAttribute', 'CitizenAgentBase', 'AgentType', 'AgentToolbox', 'FirmAg...

```{autodoc2-docstring} agentsociety.agent.__all__
```

````
