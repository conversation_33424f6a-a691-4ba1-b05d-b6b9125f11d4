# {py:mod}`agentsociety.llm`

```{py:module} agentsociety.llm
```

```{autodoc2-docstring} agentsociety.llm
:allowtitles:
```

## Submodules

```{toctree}
:titlesonly:
:maxdepth: 1

agentsociety.llm.llm
agentsociety.llm.embeddings
agentsociety.llm.utils
```

## Package Contents

### Data

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`__all__ <agentsociety.llm.__all__>`
  - ```{autodoc2-docstring} agentsociety.llm.__all__
    :summary:
    ```
````

### API

````{py:data} __all__
:canonical: agentsociety.llm.__all__
:value: >
   ['LLM', 'SentenceEmbedding', 'SimpleEmbedding', 'init_embedding', 'LLMConfig', 'LLMProviderType', 'm...

```{autodoc2-docstring} agentsociety.llm.__all__
```

````
