# {py:mod}`agentsociety.webapi.models._base`

```{py:module} agentsociety.webapi.models._base
```

```{autodoc2-docstring} agentsociety.webapi.models._base
:allowtitles:
```

## Module Contents

### Data

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`__all__ <agentsociety.webapi.models._base.__all__>`
  - ```{autodoc2-docstring} agentsociety.webapi.models._base.__all__
    :summary:
    ```
* - {py:obj}`Base <agentsociety.webapi.models._base.Base>`
  - ```{autodoc2-docstring} agentsociety.webapi.models._base.Base
    :summary:
    ```
* - {py:obj}`BaseNoInit <agentsociety.webapi.models._base.BaseNoInit>`
  - ```{autodoc2-docstring} agentsociety.webapi.models._base.BaseNoInit
    :summary:
    ```
* - {py:obj}`TABLE_PREFIX <agentsociety.webapi.models._base.TABLE_PREFIX>`
  - ```{autodoc2-docstring} agentsociety.webapi.models._base.TABLE_PREFIX
    :summary:
    ```
````

### API

````{py:data} __all__
:canonical: agentsociety.webapi.models._base.__all__
:value: >
   ['Base', 'BaseNoInit', 'TABLE_PREFIX']

```{autodoc2-docstring} agentsociety.webapi.models._base.__all__
```

````

````{py:data} Base
:canonical: agentsociety.webapi.models._base.Base
:value: >
   'declarative_base(...)'

```{autodoc2-docstring} agentsociety.webapi.models._base.Base
```

````

````{py:data} BaseNoInit
:canonical: agentsociety.webapi.models._base.BaseNoInit
:value: >
   'declarative_base(...)'

```{autodoc2-docstring} agentsociety.webapi.models._base.BaseNoInit
```

````

````{py:data} TABLE_PREFIX
:canonical: agentsociety.webapi.models._base.TABLE_PREFIX
:value: >
   'as_'

```{autodoc2-docstring} agentsociety.webapi.models._base.TABLE_PREFIX
```

````
