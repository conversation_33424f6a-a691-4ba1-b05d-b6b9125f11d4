# {py:mod}`agentsociety.cityagent`

```{py:module} agentsociety.cityagent
```

```{autodoc2-docstring} agentsociety.cityagent
:allowtitles:
```

## Subpackages

```{toctree}
:titlesonly:
:maxdepth: 3

agentsociety.cityagent.blocks
```

## Submodules

```{toctree}
:titlesonly:
:maxdepth: 1

agentsociety.cityagent.societyagent
agentsociety.cityagent.memory_config
agentsociety.cityagent.initial
agentsociety.cityagent.governmentagent
agentsociety.cityagent.bankagent
agentsociety.cityagent.sharing_params
agentsociety.cityagent.firmagent
agentsociety.cityagent.metrics
agentsociety.cityagent.nbsagent
```

## Package Contents

### Functions

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`_fill_in_agent_class_and_memory_config <agentsociety.cityagent._fill_in_agent_class_and_memory_config>`
  - ```{autodoc2-docstring} agentsociety.cityagent._fill_in_agent_class_and_memory_config
    :summary:
    ```
* - {py:obj}`default <agentsociety.cityagent.default>`
  - ```{autodoc2-docstring} agentsociety.cityagent.default
    :summary:
    ```
````

### Data

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`__all__ <agentsociety.cityagent.__all__>`
  - ```{autodoc2-docstring} agentsociety.cityagent.__all__
    :summary:
    ```
* - {py:obj}`BLOCK_MAPPING <agentsociety.cityagent.BLOCK_MAPPING>`
  - ```{autodoc2-docstring} agentsociety.cityagent.BLOCK_MAPPING
    :summary:
    ```
````

### API

````{py:data} __all__
:canonical: agentsociety.cityagent.__all__
:value: >
   ['default', 'SocietyAgent', 'FirmAgent', 'BankAgent', 'NBSAgent', 'GovernmentAgent', 'memory_config_...

```{autodoc2-docstring} agentsociety.cityagent.__all__
```

````

````{py:data} BLOCK_MAPPING
:canonical: agentsociety.cityagent.BLOCK_MAPPING
:value: >
   None

```{autodoc2-docstring} agentsociety.cityagent.BLOCK_MAPPING
```

````

````{py:function} _fill_in_agent_class_and_memory_config(self: agentsociety.configs.AgentConfig)
:canonical: agentsociety.cityagent._fill_in_agent_class_and_memory_config

```{autodoc2-docstring} agentsociety.cityagent._fill_in_agent_class_and_memory_config
```
````

````{py:function} default(config: agentsociety.configs.Config) -> agentsociety.configs.Config
:canonical: agentsociety.cityagent.default

```{autodoc2-docstring} agentsociety.cityagent.default
```
````
