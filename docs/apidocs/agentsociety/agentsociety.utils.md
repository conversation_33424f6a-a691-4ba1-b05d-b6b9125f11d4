# {py:mod}`agentsociety.utils`

```{py:module} agentsociety.utils
```

```{autodoc2-docstring} agentsociety.utils
:allowtitles:
```

## Subpackages

```{toctree}
:titlesonly:
:maxdepth: 3

agentsociety.utils.parsers
```

## Submodules

```{toctree}
:titlesonly:
:maxdepth: 1

agentsociety.utils.llm
agentsociety.utils.decorators
```

## Package Contents

### Data

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`__all__ <agentsociety.utils.__all__>`
  - ```{autodoc2-docstring} agentsociety.utils.__all__
    :summary:
    ```
* - {py:obj}`NONE_SENDER_ID <agentsociety.utils.NONE_SENDER_ID>`
  - ```{autodoc2-docstring} agentsociety.utils.NONE_SENDER_ID
    :summary:
    ```
````

### API

````{py:data} __all__
:canonical: agentsociety.utils.__all__
:value: >
   ['NONE_SENDER_ID']

```{autodoc2-docstring} agentsociety.utils.__all__
```

````

````{py:data} NONE_SENDER_ID
:canonical: agentsociety.utils.NONE_SENDER_ID
:value: >
   'none'

```{autodoc2-docstring} agentsociety.utils.NONE_SENDER_ID
```

````
