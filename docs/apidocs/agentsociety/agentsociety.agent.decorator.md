# {py:mod}`agentsociety.agent.decorator`

```{py:module} agentsociety.agent.decorator
```

```{autodoc2-docstring} agentsociety.agent.decorator
:allowtitles:
```

## Module Contents

### Functions

````{list-table}
:class: autosummary longtable
:align: left

* - {py:obj}`register_get <agentsociety.agent.decorator.register_get>`
  - ```{autodoc2-docstring} agentsociety.agent.decorator.register_get
    :summary:
    ```
* - {py:obj}`param_docs <agentsociety.agent.decorator.param_docs>`
  - ```{autodoc2-docstring} agentsociety.agent.decorator.param_docs
    :summary:
    ```
````

### API

````{py:function} register_get(description: str)
:canonical: agentsociety.agent.decorator.register_get

```{autodoc2-docstring} agentsociety.agent.decorator.register_get
```
````

````{py:function} param_docs(**param_descriptions: str)
:canonical: agentsociety.agent.decorator.param_docs

```{autodoc2-docstring} agentsociety.agent.decorator.param_docs
```
````
